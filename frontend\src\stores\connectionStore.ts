import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ApiService, ConnectionInfo, RedisConnectionConfig } from '../services/api';

interface ConnectionState {
  // 状态
  connections: ConnectionInfo[];
  activeConnectionId: string | null;
  loading: boolean;
  error: string | null;

  // 操作
  fetchConnections: () => Promise<void>;
  createConnection: (config: RedisConnectionConfig) => Promise<string | null>;
  testConnection: (config: RedisConnectionConfig) => Promise<boolean>;
  deleteConnection: (id: string) => Promise<void>;
  setActiveConnection: (id: string | null) => void;
  clearError: () => void;
}

export const useConnectionStore = create<ConnectionState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      connections: [],
      activeConnectionId: null,
      loading: false,
      error: null,

      // 获取所有连接
      fetchConnections: async () => {
        set({ loading: true, error: null });
        try {
          const response = await ApiService.getConnections();
          if (response.success && response.data) {
            set({ connections: response.data, loading: false });
          } else {
            set({ error: response.error || 'Failed to fetch connections', loading: false });
          }
        } catch (error: any) {
          set({ 
            error: error.response?.data?.error || error.message || 'Failed to fetch connections',
            loading: false 
          });
        }
      },

      // 创建新连接
      createConnection: async (config: RedisConnectionConfig) => {
        set({ loading: true, error: null });
        try {
          const response = await ApiService.createConnection(config);
          if (response.success && response.data) {
            // 重新获取连接列表
            await get().fetchConnections();
            set({ loading: false });
            return response.data.connectionId;
          } else {
            set({ error: response.error || 'Failed to create connection', loading: false });
            return null;
          }
        } catch (error: any) {
          set({ 
            error: error.response?.data?.error || error.message || 'Failed to create connection',
            loading: false 
          });
          return null;
        }
      },

      // 测试连接
      testConnection: async (config: RedisConnectionConfig) => {
        set({ loading: true, error: null });
        try {
          const response = await ApiService.testConnection(config);
          set({ loading: false });
          if (response.success && response.data) {
            return response.data.connectable;
          } else {
            set({ error: response.error || 'Connection test failed' });
            return false;
          }
        } catch (error: any) {
          set({ 
            error: error.response?.data?.error || error.message || 'Connection test failed',
            loading: false 
          });
          return false;
        }
      },

      // 删除连接
      deleteConnection: async (id: string) => {
        set({ loading: true, error: null });
        try {
          const response = await ApiService.deleteConnection(id);
          if (response.success) {
            // 如果删除的是当前活动连接，清除活动连接
            const { activeConnectionId } = get();
            if (activeConnectionId === id) {
              set({ activeConnectionId: null });
            }
            // 重新获取连接列表
            await get().fetchConnections();
            set({ loading: false });
          } else {
            set({ error: response.error || 'Failed to delete connection', loading: false });
          }
        } catch (error: any) {
          set({ 
            error: error.response?.data?.error || error.message || 'Failed to delete connection',
            loading: false 
          });
        }
      },

      // 设置活动连接
      setActiveConnection: (id: string | null) => {
        set({ activeConnectionId: id });
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'connection-store',
    }
  )
);

// 获取活动连接信息的辅助函数
export const useActiveConnection = () => {
  const { connections, activeConnectionId } = useConnectionStore();
  return connections.find(conn => conn.id === activeConnectionId) || null;
};
