import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import { useConnectionStore, useActiveConnection } from './stores/connectionStore';
import { useRedisStore } from './stores/redisStore';
import { useTheme, initializeTheme } from './stores/themeStore';
import AppleLayout from './components/Layout/AppleLayout';
import AppleConnectionManager from './components/Connection/AppleConnectionManager';
import AppleDataBrowser from './components/DataBrowser/AppleDataBrowser';
import AppleCommandExecutor from './components/CommandExecutor/AppleCommandExecutor';
import DataBrowser from './components/DataBrowser';
import CommandExecutor from './components/CommandExecutor';
import PerformanceMonitor from './components/PerformanceMonitor';
import DataImportExport from './components/DataImportExport';
import ShortcutsHelp from './components/ShortcutsHelp';
import HelpDocumentation from './components/HelpDocumentation';
import RealtimeStatusIndicator from './components/RealtimeStatusIndicator';
import './styles/apple-design-system.css';

type TabKey = 'browser' | 'command' | 'monitor' | 'import-export';

function App() {
  const [showConnectionManager, setShowConnectionManager] = useState(false);
  const [activeTab, setActiveTab] = useState<TabKey>('browser');
  const [showShortcutsHelp, setShowShortcutsHelp] = useState(false);
  const [showHelpDocumentation, setShowHelpDocumentation] = useState(false);

  const { activeConnectionId, setActiveConnection, fetchConnections } = useConnectionStore();
  const { reset: resetRedisStore } = useRedisStore();
  const activeConnection = useActiveConnection();
  const { toggleTheme } = useTheme();

  useEffect(() => {
    // 初始化时获取连接列表
    fetchConnections();
    // 初始化主题
    initializeTheme();
  }, [fetchConnections]);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl + T: 切换主题
      if (event.ctrlKey && event.key === 't') {
        event.preventDefault();
        toggleTheme();
        return;
      }

      // Ctrl + K: 显示快捷键帮助
      if (event.ctrlKey && event.key === 'k') {
        event.preventDefault();
        setShowShortcutsHelp(true);
        return;
      }

      // Shift + ?: 显示帮助文档
      if (event.shiftKey && event.key === '?') {
        event.preventDefault();
        setShowHelpDocumentation(true);
        return;
      }

      // Ctrl + N: 新建连接
      if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        setShowConnectionManager(true);
        return;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [toggleTheme]);

  const handleConnect = (connectionId: string) => {
    setActiveConnection(connectionId);
    message.success('连接已切换');
  };

  const handleDisconnect = () => {
    setActiveConnection(null);
    resetRedisStore();
    message.info('已断开连接');
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      connected: 'success',
      disconnected: 'default',
      connecting: 'processing',
      error: 'error',
    };
    return colorMap[status as keyof typeof colorMap] || 'default';
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'browser':
        return <AppleDataBrowser />;
      case 'command':
        return <AppleCommandExecutor />;
      case 'monitor':
        return <PerformanceMonitor />;
      case 'import-export':
        return <DataImportExport />;
      default:
        return <AppleDataBrowser />;
    }
  };

  return (
    <>
      <AppleLayout
        activeTab={activeTab}
        onTabChange={setActiveTab}
        onShowConnectionManager={() => setShowConnectionManager(true)}
        onDisconnect={handleDisconnect}
      >
        {renderContent()}
      </AppleLayout>

      <AppleConnectionManager
        visible={showConnectionManager}
        onClose={() => setShowConnectionManager(false)}
        onConnect={handleConnect}
      />

      <ShortcutsHelp
        visible={showShortcutsHelp}
        onClose={() => setShowShortcutsHelp(false)}
      />

      <HelpDocumentation
        visible={showHelpDocumentation}
        onClose={() => setShowHelpDocumentation(false)}
      />
    </>
  );
}

export default App;
