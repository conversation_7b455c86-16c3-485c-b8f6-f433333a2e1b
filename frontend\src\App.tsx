import React, { useState, useEffect } from 'react';
import { Layout, Typography, Button, Menu, Space, Badge, message } from 'antd';
import {
  DatabaseOutlined,
  TableOutlined,
  CodeOutlined,
  ThunderboltOutlined,
  ImportOutlined,
  DisconnectOutlined,
} from '@ant-design/icons';
import { useConnectionStore, useActiveConnection } from './stores/connectionStore';
import { useRedisStore } from './stores/redisStore';
import { useTheme, initializeTheme } from './stores/themeStore';
import ConnectionManager from './components/ConnectionManager';
import DataBrowser from './components/DataBrowser';
import CommandExecutor from './components/CommandExecutor';
import PerformanceMonitor from './components/PerformanceMonitor';
import DataImportExport from './components/DataImportExport';
import RealtimeStatusIndicator from './components/RealtimeStatusIndicator';
import ThemeToggle from './components/ThemeToggle';
import ShortcutsHelp from './components/ShortcutsHelp';
import HelpDocumentation from './components/HelpDocumentation';

const { Header, Content, Sider } = Layout;
const { Title } = Typography;

type TabKey = 'browser' | 'command' | 'monitor' | 'import-export';

function App() {
  const [showConnectionManager, setShowConnectionManager] = useState(false);
  const [activeTab, setActiveTab] = useState<TabKey>('browser');
  const [showShortcutsHelp, setShowShortcutsHelp] = useState(false);
  const [showHelpDocumentation, setShowHelpDocumentation] = useState(false);

  const { activeConnectionId, setActiveConnection, fetchConnections } = useConnectionStore();
  const { reset: resetRedisStore } = useRedisStore();
  const activeConnection = useActiveConnection();
  const { toggleTheme } = useTheme();

  useEffect(() => {
    // 初始化时获取连接列表
    fetchConnections();
    // 初始化主题
    initializeTheme();
  }, [fetchConnections]);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl + T: 切换主题
      if (event.ctrlKey && event.key === 't') {
        event.preventDefault();
        toggleTheme();
        return;
      }

      // Ctrl + K: 显示快捷键帮助
      if (event.ctrlKey && event.key === 'k') {
        event.preventDefault();
        setShowShortcutsHelp(true);
        return;
      }

      // Shift + ?: 显示帮助文档
      if (event.shiftKey && event.key === '?') {
        event.preventDefault();
        setShowHelpDocumentation(true);
        return;
      }

      // Ctrl + N: 新建连接
      if (event.ctrlKey && event.key === 'n') {
        event.preventDefault();
        setShowConnectionManager(true);
        return;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [toggleTheme]);

  const handleConnect = (connectionId: string) => {
    setActiveConnection(connectionId);
    message.success('连接已切换');
  };

  const handleDisconnect = () => {
    setActiveConnection(null);
    resetRedisStore();
    message.info('已断开连接');
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      connected: 'success',
      disconnected: 'default',
      connecting: 'processing',
      error: 'error',
    };
    return colorMap[status as keyof typeof colorMap] || 'default';
  };

  const renderContent = () => {
    if (!activeConnectionId) {
      return (
        <div className="flex items-center justify-center h-full bg-gray-50">
          <div className="text-center">
            <DatabaseOutlined className="text-6xl text-blue-400 mb-4" />
            <Title level={2}>欢迎使用 Redis Client Solo</Title>
            <p className="text-lg text-gray-600 mb-6">
              现代化的Redis可视化管理工具，让Redis管理变得简单高效
            </p>
            <Button
              type="primary"
              size="large"
              icon={<DatabaseOutlined />}
              onClick={() => setShowConnectionManager(true)}
            >
              开始连接Redis服务器
            </Button>
          </div>
        </div>
      );
    }

    switch (activeTab) {
      case 'browser':
        return <DataBrowser />;
      case 'command':
        return <CommandExecutor />;
      case 'monitor':
        return <PerformanceMonitor />;
      case 'import-export':
        return <DataImportExport />;
      default:
        return <DataBrowser />;
    }
  };

  return (
    <Layout className="min-h-screen">
      <Header className="bg-white shadow-sm border-b px-4">
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <DatabaseOutlined className="text-2xl text-blue-600" />
              <Title level={3} className="mb-0">Redis Client Solo</Title>
            </div>

            {activeConnection && (
              <div className="flex items-center space-x-2">
                <Badge
                  status={getStatusColor(activeConnection.status) as any}
                  text={
                    <span className="text-sm">
                      {activeConnection.name} ({activeConnection.host}:{activeConnection.port})
                    </span>
                  }
                />
              </div>
            )}
          </div>

          <Space>
            <RealtimeStatusIndicator size="small" />
            <ThemeToggle />
            <Button
              type="text"
              onClick={() => setShowShortcutsHelp(true)}
              title="快捷键帮助 (Ctrl+K)"
            >
              快捷键
            </Button>
            <Button
              type="text"
              onClick={() => setShowHelpDocumentation(true)}
              title="帮助文档 (Shift+?)"
            >
              帮助
            </Button>
            {activeConnectionId && (
              <Button
                icon={<DisconnectOutlined />}
                onClick={handleDisconnect}
              >
                断开连接
              </Button>
            )}
            <Button
              type="primary"
              icon={<DatabaseOutlined />}
              onClick={() => setShowConnectionManager(true)}
            >
              连接管理
            </Button>
          </Space>
        </div>
      </Header>

      <Layout>
        {activeConnectionId && (
          <Sider width={200} className="bg-white border-r">
            <Menu
              mode="inline"
              selectedKeys={[activeTab]}
              onClick={({ key }) => setActiveTab(key as TabKey)}
              className="h-full border-r-0"
              items={[
                {
                  key: 'browser',
                  icon: <TableOutlined />,
                  label: '数据浏览器',
                },
                {
                  key: 'command',
                  icon: <CodeOutlined />,
                  label: '命令执行器',
                },
                {
                  key: 'monitor',
                  icon: <ThunderboltOutlined />,
                  label: '性能监控',
                },
                {
                  key: 'import-export',
                  icon: <ImportOutlined />,
                  label: '导入导出',
                },
              ]}
            />
          </Sider>
        )}

        <Content className="bg-white">
          {renderContent()}
        </Content>
      </Layout>

      <ConnectionManager
        visible={showConnectionManager}
        onClose={() => setShowConnectionManager(false)}
        onConnect={handleConnect}
      />

      <ShortcutsHelp
        visible={showShortcutsHelp}
        onClose={() => setShowShortcutsHelp(false)}
      />

      <HelpDocumentation
        visible={showHelpDocumentation}
        onClose={() => setShowHelpDocumentation(false)}
      />
    </Layout>
  );
}

export default App;
