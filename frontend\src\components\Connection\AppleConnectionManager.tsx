import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Card,
  Badge,
  Space,
  Typography,
  Divider,
  message,
  Tooltip,
  Empty,
} from 'antd';
import {
  DatabaseOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { useConnectionStore } from '../../stores/connectionStore';
import { RedisConnectionConfig } from '../../services/api';

const { Title, Text, Paragraph } = Typography;
const { Password } = Input;

interface AppleConnectionManagerProps {
  visible: boolean;
  onClose: () => void;
  onConnect: (connectionId: string) => void;
}

const AppleConnectionManager: React.FC<AppleConnectionManagerProps> = ({
  visible,
  onClose,
  onConnect,
}) => {
  const [form] = Form.useForm();
  const [showForm, setShowForm] = useState(false);
  const [editingConnection, setEditingConnection] = useState<string | null>(null);
  const [testingConnection, setTestingConnection] = useState<string | null>(null);

  const {
    connections,
    loading,
    fetchConnections,
    createConnection,
    updateConnection,
    deleteConnection,
    testConnection,
  } = useConnectionStore();

  useEffect(() => {
    if (visible) {
      fetchConnections();
    }
  }, [visible, fetchConnections]);

  // 获取连接状态样式
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'connected':
        return {
          color: 'var(--apple-success)',
          icon: <CheckCircleOutlined />,
          text: '已连接',
          badge: 'success' as const,
        };
      case 'connecting':
        return {
          color: 'var(--apple-warning)',
          icon: <ExclamationCircleOutlined />,
          text: '连接中',
          badge: 'processing' as const,
        };
      case 'error':
        return {
          color: 'var(--apple-error)',
          icon: <CloseCircleOutlined />,
          text: '连接失败',
          badge: 'error' as const,
        };
      default:
        return {
          color: 'var(--text-tertiary)',
          icon: <DatabaseOutlined />,
          text: '未连接',
          badge: 'default' as const,
        };
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      const config: Omit<RedisConnectionConfig, 'id'> = {
        name: values.name,
        host: values.host,
        port: values.port,
        password: values.password || undefined,
        database: values.database || 0,
        ssl: values.ssl || false,
      };

      if (editingConnection) {
        await updateConnection(editingConnection, config);
        message.success('连接配置已更新');
      } else {
        await createConnection(config);
        message.success('连接配置已创建');
      }

      setShowForm(false);
      setEditingConnection(null);
      form.resetFields();
    } catch (error: any) {
      message.error(error.message || '操作失败');
    }
  };

  // 处理编辑连接
  const handleEdit = (connection: any) => {
    setEditingConnection(connection.id);
    form.setFieldsValue({
      name: connection.name,
      host: connection.host,
      port: connection.port,
      password: connection.password,
      database: connection.database,
      ssl: connection.ssl,
    });
    setShowForm(true);
  };

  // 处理删除连接
  const handleDelete = async (connectionId: string) => {
    try {
      await deleteConnection(connectionId);
      message.success('连接已删除');
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 处理测试连接
  const handleTest = async (connectionId: string) => {
    setTestingConnection(connectionId);
    try {
      const result = await testConnection(connectionId);
      if (result.success) {
        message.success('连接测试成功');
      } else {
        message.error(`连接测试失败: ${result.error}`);
      }
    } catch (error: any) {
      message.error(error.message || '连接测试失败');
    } finally {
      setTestingConnection(null);
    }
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      className="apple-modal"
      styles={{
        body: { padding: 0 },
        content: { borderRadius: 'var(--radius-2xl)' },
      }}
    >
      <div className="p-6">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <Title level={3} className="mb-1">
              连接管理
            </Title>
            <Text className="text-[var(--text-secondary)]">
              管理您的Redis连接配置
            </Text>
          </div>
          
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setShowForm(true);
              setEditingConnection(null);
              form.resetFields();
            }}
            className="apple-button apple-button-primary"
          >
            新建连接
          </Button>
        </div>

        {/* 连接列表 */}
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {connections.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={
                <div>
                  <Text className="text-[var(--text-secondary)]">
                    还没有任何连接配置
                  </Text>
                  <br />
                  <Button
                    type="link"
                    onClick={() => setShowForm(true)}
                    className="p-0 h-auto"
                  >
                    创建第一个连接
                  </Button>
                </div>
              }
            />
          ) : (
            connections.map((connection) => {
              const statusConfig = getStatusConfig(connection.status);
              
              return (
                <Card
                  key={connection.id}
                  className="apple-card hover:shadow-md transition-all duration-200"
                  bodyStyle={{ padding: '20px' }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 flex-1">
                      {/* 连接图标和状态 */}
                      <div className="relative">
                        <div
                          className="w-12 h-12 rounded-xl flex items-center justify-center"
                          style={{ backgroundColor: `${statusConfig.color}15` }}
                        >
                          <DatabaseOutlined
                            className="text-xl"
                            style={{ color: statusConfig.color }}
                          />
                        </div>
                        <Badge
                          status={statusConfig.badge}
                          className="absolute -top-1 -right-1"
                        />
                      </div>

                      {/* 连接信息 */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <Text strong className="text-[var(--text-primary)]">
                            {connection.name}
                          </Text>
                          <Badge
                            color={statusConfig.color}
                            text={statusConfig.text}
                          />
                        </div>
                        
                        <div className="text-sm text-[var(--text-secondary)] space-y-1">
                          <div>
                            {connection.host}:{connection.port}
                            {connection.ssl && (
                              <span className="ml-2 px-2 py-0.5 bg-[var(--apple-success)] text-white text-xs rounded">
                                SSL
                              </span>
                            )}
                          </div>
                          <div>数据库: {connection.database}</div>
                          {connection.lastError && (
                            <div className="text-[var(--apple-error)] text-xs">
                              错误: {connection.lastError}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center space-x-2">
                      <Tooltip title="连接">
                        <Button
                          type="text"
                          icon={<PlayCircleOutlined />}
                          onClick={() => onConnect(connection.id)}
                          disabled={connection.status === 'error'}
                          className="text-[var(--apple-success)] hover:bg-[var(--apple-success)]15"
                        />
                      </Tooltip>
                      
                      <Tooltip title="测试连接">
                        <Button
                          type="text"
                          icon={<CheckCircleOutlined />}
                          onClick={() => handleTest(connection.id)}
                          loading={testingConnection === connection.id}
                          className="text-[var(--apple-blue-500)] hover:bg-[var(--apple-blue-500)]15"
                        />
                      </Tooltip>
                      
                      <Tooltip title="编辑">
                        <Button
                          type="text"
                          icon={<EditOutlined />}
                          onClick={() => handleEdit(connection)}
                          className="text-[var(--text-secondary)] hover:bg-[var(--bg-tertiary)]"
                        />
                      </Tooltip>
                      
                      <Tooltip title="删除">
                        <Button
                          type="text"
                          icon={<DeleteOutlined />}
                          onClick={() => handleDelete(connection.id)}
                          className="text-[var(--apple-error)] hover:bg-[var(--apple-error)]15"
                        />
                      </Tooltip>
                    </div>
                  </div>
                </Card>
              );
            })
          )}
        </div>

        {/* 连接表单 */}
        {showForm && (
          <>
            <Divider />
            <div>
              <Title level={4} className="mb-4">
                {editingConnection ? '编辑连接' : '新建连接'}
              </Title>
              
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={{
                  port: 6379,
                  database: 0,
                  ssl: false,
                }}
              >
                <div className="grid grid-cols-2 gap-4">
                  <Form.Item
                    name="name"
                    label="连接名称"
                    rules={[{ required: true, message: '请输入连接名称' }]}
                  >
                    <Input
                      placeholder="例如：本地Redis"
                      className="apple-input"
                    />
                  </Form.Item>
                  
                  <Form.Item
                    name="host"
                    label="主机地址"
                    rules={[{ required: true, message: '请输入主机地址' }]}
                  >
                    <Input
                      placeholder="localhost"
                      className="apple-input"
                    />
                  </Form.Item>
                  
                  <Form.Item
                    name="port"
                    label="端口"
                    rules={[{ required: true, message: '请输入端口' }]}
                  >
                    <InputNumber
                      min={1}
                      max={65535}
                      placeholder="6379"
                      className="w-full apple-input"
                    />
                  </Form.Item>
                  
                  <Form.Item
                    name="database"
                    label="数据库"
                  >
                    <InputNumber
                      min={0}
                      max={15}
                      placeholder="0"
                      className="w-full apple-input"
                    />
                  </Form.Item>
                </div>
                
                <Form.Item
                  name="password"
                  label="密码"
                >
                  <Password
                    placeholder="可选"
                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                    className="apple-input"
                  />
                </Form.Item>
                
                <Form.Item
                  name="ssl"
                  valuePropName="checked"
                >
                  <div className="flex items-center space-x-2">
                    <Switch />
                    <Text>启用SSL连接</Text>
                  </div>
                </Form.Item>
                
                <div className="flex justify-end space-x-3 mt-6">
                  <Button
                    onClick={() => {
                      setShowForm(false);
                      setEditingConnection(null);
                      form.resetFields();
                    }}
                    className="apple-button apple-button-secondary"
                  >
                    取消
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    className="apple-button apple-button-primary"
                  >
                    {editingConnection ? '更新连接' : '创建连接'}
                  </Button>
                </div>
              </Form>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default AppleConnectionManager;
